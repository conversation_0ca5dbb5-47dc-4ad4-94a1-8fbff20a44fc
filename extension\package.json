{"private": true, "name": "grammarly", "publisher": "znck", "displayName": "Grammarly", "description": "A grammar checking for Visual Studio Code using Grammarly.", "version": "0.24.0", "icon": "assets/logo.png", "preview": true, "engines": {"vscode": "^1.63.0"}, "repository": {"type": "git", "url": "https://github.com/znck/grammarly", "directory": "extension"}, "categories": ["Other"], "keywords": ["Grammarly", "grammar", "spellcheck", "writing assistant", "writing"], "activationEvents": ["onCommand:grammarly.check", "onCommand:grammarly.login", "onCommand:grammarly.logout", "onLanguage:plaintext", "onLanguage:markdown", "onLanguage:html"], "contributes": {"configuration": {"title": "Grammarly", "properties": {"grammarly.patterns": {"type": "array", "description": "A glob pattern, like `*.{md,txt}` for file scheme.", "items": {"type": "string"}, "default": ["**/readme.md", "**/README.md", "**/*.txt"], "required": true, "scope": "window", "order": 0, "markdownDeprecationMessage": "Use [Files: Include](#grammarly.files.include#)"}, "grammarly.files.include": {"type": "array", "markdownDescription": "Configure [glob patterns](https://code.visualstudio.com/docs/editor/codebasics#_advanced-search-options) for including files and folders.", "items": {"type": "string"}, "default": ["**/readme.md", "**/README.md", "**/*.txt"], "required": true, "scope": "window", "order": 1}, "grammarly.files.exclude": {"type": "array", "markdownDescription": "Configure [glob patterns](https://code.visualstudio.com/docs/editor/codebasics#_advanced-search-options) for excluding files and folders.", "items": {"type": "string"}, "default": [], "required": true, "scope": "window", "order": 2}, "grammarly.selectors": {"type": "array", "description": "Filter documents to be checked with <PERSON><PERSON>.", "items": {"type": "object", "properties": {"scheme": {"type": "string", "description": "A Uri scheme, like `file` or `untitled`."}, "language": {"type": "string", "description": "A language id, like `typescript`."}, "pattern": {"type": "string", "description": "A glob pattern, like `*.{md,txt}`."}}}, "default": [], "required": true, "scope": "window", "order": 99}, "grammarly.startTextCheckInPausedState": {"type": "boolean", "description": "Start text checking session in paused state", "default": false}, "grammarly.config.documentDialect": {"markdownDescription": "Specific variety of English being written. See [this article](https://support.grammarly.com/hc/en-us/articles/115000089992-Select-between-British-English-American-English-Canadian-English-and-Australian-English) for differences.", "enum": ["american", "australian", "british", "canadian", "auto-text"], "enumDescriptions": ["", "", "", "", "An appropriate value based on the text."], "default": "auto-text", "scope": "language-overridable"}, "grammarly.config.documentDomain": {"markdownDescription": "The style or type of writing to be checked. See [What is domain/document type](https://support.grammarly.com/hc/en-us/articles/115000091472-What-is-domain-document-type-)?", "enum": ["academic", "business", "general", "mail", "casual", "creative"], "enumDescriptions": ["Academic is the strictest style of writing. On top of catching grammar and punctuation issues, <PERSON><PERSON> will make suggestions around passive voice, contractions, and informal pronouns (I, you), and will point out unclear antecedents (e.g., sentences starting with “This is…”).", "The business style setting checks the text against formal writing criteria. However, unlike the Academic domain, it allows the use of some informal expressions, informal pronouns, and unclear antecedents.", "This is the default style and uses a medium level of strictness.", "The email genre is similar to the General domain and helps ensure that your email communication is engaging. In addition to catching grammar, spelling, and punctuation mistakes, <PERSON><PERSON> also points out the use of overly direct language that may sound harsh to a reader.", "Casual is designed for informal types of writing and ignores most style issues. It does not flag contractions, passive voice, informal pronouns, who-versus-whom usage, split infinitives, or run-on sentences. This style is suitable for personal communication.", "This is the most permissive style. It catches grammar, punctuation, and spelling mistakes but allows some leeway for those who want to intentionally bend grammar rules to achieve certain effects. Creative doesn’t flag sentence fragments (missing subjects or verbs), wordy sentences, colloquialisms, informal pronouns, passive voice, incomplete comparisons, or run-on sentences."], "default": "general", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.conjugationAtStartOfSentence": {"description": "Flags use of conjunctions such as \"but\" and \"and\" at the beginning of sentences.", "enum": ["on", "off"], "default": "off", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.fluency": {"description": "Suggests ways to sound more natural and fluent.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.informalPronounsAcademic": {"description": "Flags use of personal pronouns such as \"I\" and \"you\" in academic writing.", "enum": ["on", "off"], "default": "off", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.missingSpaces": {"description": "Suggests adding missing spacing after a numeral when writing times.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.nounStrings": {"description": "Flags a series of nouns that modify a final noun.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.numbersBeginningSentences": {"description": "Suggests spelling out numbers at the beginning of sentences.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.numbersZeroThroughTen": {"description": "Suggests spelling out numbers zero through ten.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.oxfordComma": {"description": "Suggests adding the Oxford comma after the second-to-last item in a list of things.", "enum": ["on", "off"], "default": "off", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.passiveVoice": {"description": "Flags use of passive voice.", "enum": ["on", "off"], "default": "off", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.personFirstLanguage": {"description": "Suggests using person-first language to refer respectfully to an individual with a disability.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.possiblyBiasedLanguageAgeRelated": {"description": "Suggests alternatives to potentially biased language related to older adults.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.possiblyBiasedLanguageDisabilityRelated": {"description": "Suggests alternatives to potentially ableist language.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.possiblyBiasedLanguageFamilyRelated": {"description": "Suggests alternatives to potentially biased language related to parenting and family systems.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.possiblyBiasedLanguageGenderRelated": {"description": "Suggests alternatives to potentially gender-biased and non-inclusive phrasing.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.possiblyBiasedLanguageHumanRights": {"description": "Suggests alternatives to language related to human slavery.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.possiblyBiasedLanguageHumanRightsRelated": {"description": "Suggests alternatives to terms with origins in the institution of slavery.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.possiblyBiasedLanguageLGBTQIARelated": {"description": "Flags LGBTQIA+-related terms that may be seen as biased, outdated, or disrespectful in some contexts.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.possiblyBiasedLanguageRaceEthnicityRelated": {"description": "Suggests alternatives to potentially biased language related to race and ethnicity.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.possiblyPoliticallyIncorrectLanguage": {"description": "Suggests alternatives to language that may be considered politically incorrect.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.prepositionAtTheEndOfSentence": {"description": "Flags use of prepositions such as \"with\" and \"in\" at the end of sentences.", "enum": ["on", "off"], "default": "off", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.punctuationWithQuotation": {"description": "Suggests placing punctuation before closing quotation marks.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.readabilityFillerWords": {"description": "Flags long, complicated sentences that could potentially confuse your reader.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.readabilityTransforms": {"description": "Suggests splitting long, complicated sentences that could potentially confuse your reader.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.sentenceVariety": {"description": "Flags series of sentences that follow the same pattern.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.spacesSurroundingSlash": {"description": "Suggests removing extra spaces surrounding a slash.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.splitInfinitive": {"description": "Suggests rewriting split infinitives so that an adverb doesn't come between \"to\" and the verb.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.stylisticFragments": {"description": "Suggests completing all incomplete sentences, including stylistic sentence fragments that may be intentional.", "enum": ["on", "off"], "default": "off", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.unnecessaryEllipses": {"description": "Flags unnecessary use of ellipses (...).", "enum": ["on", "off"], "default": "off", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.variety": {"description": "Suggests alternatives to words that occur frequently in the same paragraph.", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestionCategories.vocabulary": {"description": "Suggests alternatives to bland and overused words such as \"good\" and \"nice\".", "enum": ["on", "off"], "default": "on", "scope": "language-overridable"}, "grammarly.config.suggestions.ConjunctionAtStartOfSentence": {"description": "Flags use of conjunctions such as 'but' and 'and' at the beginning of sentences.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.conjunctionAtStartOfSentence` instead."}, "grammarly.config.suggestions.Fluency": {"description": "Suggests ways to sound more natural and fluent.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.fluency` instead."}, "grammarly.config.suggestions.InformalPronounsAcademic": {"description": "Flags use of personal pronouns such as 'I' and 'you' in academic writing.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.informalPronounsAcademic` instead."}, "grammarly.config.suggestions.MissingSpaces": {"description": "Suggests adding missing spacing after a numeral when writing times.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.missingSpaces` instead."}, "grammarly.config.suggestions.NounStrings": {"description": "Flags a series of nouns that modify a final noun.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.nounStrings` instead."}, "grammarly.config.suggestions.NumbersBeginningSentences": {"description": "Suggests spelling out numbers at the beginning of sentences.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.numbersBeginningSentences` instead."}, "grammarly.config.suggestions.NumbersZeroThroughTen": {"description": "Suggests spelling out numbers zero through ten.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.numbersZeroThroughTen` instead."}, "grammarly.config.suggestions.OxfordComma": {"description": "Suggests adding the Oxford comma after the second-to-last item in a list of things.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.oxfordComma` instead."}, "grammarly.config.suggestions.PassiveVoice": {"description": "Flags use of passive voice.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.passiveVoice` instead."}, "grammarly.config.suggestions.PersonFirstLanguage": {"description": "Suggests using person-first language to refer respectfully to an individual with a disability.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.personFirstLanguage` instead."}, "grammarly.config.suggestions.PossiblyBiasedLanguageAgeRelated": {"description": "Suggests alternatives to potentially biased language related to older adults.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.possiblyBiasedLanguageAgeRelated` instead."}, "grammarly.config.suggestions.PossiblyBiasedLanguageDisabilityRelated": {"description": "Suggests alternatives to potentially ableist language.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.possiblyBiasedLanguageDisabilityRelated` instead."}, "grammarly.config.suggestions.PossiblyBiasedLanguageFamilyRelated": {"description": "Suggests alternatives to potentially biased language related to parenting and family systems.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.possiblyBiasedLanguageFamilyRelated` instead."}, "grammarly.config.suggestions.PossiblyBiasedLanguageGenderRelated": {"description": "Suggests alternatives to potentially gender-biased and non-inclusive phrasing.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.possiblyBiasedLanguageGenderRelated` instead."}, "grammarly.config.suggestions.PossiblyBiasedLanguageHumanRights": {"description": "Suggests alternatives to language related to human slavery.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.possiblyBiasedLanguageHumanRights` instead."}, "grammarly.config.suggestions.PossiblyBiasedLanguageHumanRightsRelated": {"description": "Suggests alternatives to terms with origins in the institution of slavery.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.possiblyBiasedLanguageHumanRightsRelated` instead."}, "grammarly.config.suggestions.PossiblyBiasedLanguageLgbtqiaRelated": {"description": "Flags LGBTQIA+-related terms that may be seen as biased, outdated, or disrespectful in some contexts.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.possiblyBiasedLanguageLgbtqiaRelated` instead."}, "grammarly.config.suggestions.PossiblyBiasedLanguageRaceEthnicityRelated": {"description": "Suggests alternatives to potentially biased language related to race and ethnicity.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.possiblyBiasedLanguageRaceEthnicityRelated` instead."}, "grammarly.config.suggestions.PossiblyPoliticallyIncorrectLanguage": {"description": "Suggests alternatives to language that may be considered politically incorrect.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.possiblyPoliticallyIncorrectLanguage` instead."}, "grammarly.config.suggestions.PrepositionAtTheEndOfSentence": {"description": "Flags use of prepositions such as 'with' and 'in' at the end of sentences.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.prepositionAtTheEndOfSentence` instead."}, "grammarly.config.suggestions.PunctuationWithQuotation": {"description": "Suggests placing punctuation before closing quotation marks.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.punctuationWithQuotation` instead."}, "grammarly.config.suggestions.ReadabilityFillerwords": {"description": "Flags long, complicated sentences that could potentially confuse your reader.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.readabilityFillerwords` instead."}, "grammarly.config.suggestions.ReadabilityTransforms": {"description": "Suggests splitting long, complicated sentences that could potentially confuse your reader.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.readabilityTransforms` instead."}, "grammarly.config.suggestions.SentenceVariety": {"description": "Flags series of sentences that follow the same pattern.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.sentenceVariety` instead."}, "grammarly.config.suggestions.SpacesSurroundingSlash": {"description": "Suggests removing extra spaces surrounding a slash.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.spacesSurroundingSlash` instead."}, "grammarly.config.suggestions.SplitInfinitive": {"description": "Suggests rewriting split infinitives so that an adverb doesn't come between 'to' and the verb.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.splitInfinitive` instead."}, "grammarly.config.suggestions.StylisticFragments": {"description": "Suggests completing all incomplete sentences, including stylistic sentence fragments that may be intentional.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.stylisticFragments` instead."}, "grammarly.config.suggestions.UnnecessaryEllipses": {"description": "Flags unnecessary use of ellipses (...).", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.unnecessaryEllipses` instead."}, "grammarly.config.suggestions.Variety": {"description": "Suggests alternatives to words that occur frequently in the same paragraph.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.variety` instead."}, "grammarly.config.suggestions.Vocabulary": {"description": "Suggests alternatives to bland and overused words such as 'good' and 'nice'.", "enum": [true, false, null], "default": null, "scope": "language-overridable", "deprecationMessage": "Use `grammarly.config.suggestionCategories.vocabulary` instead."}}}, "commands": [{"title": "Check text", "category": "Grammarly", "command": "grammarly.check", "icon": "$(pass-filled)", "enablement": "!grammarly.isActive"}, {"title": "Login / Connect your account", "category": "Grammarly", "command": "grammarly.login", "icon": "$(log-in)", "enablement": "!grammarly.isRunning || !grammarly.isUserAccountConnected"}, {"title": "Log out", "category": "Grammarly", "command": "grammarly.logout", "icon": "$(log-out)", "enablement": "grammarly.isUserAccountConnected"}, {"title": "Restart language server", "category": "Grammarly", "command": "grammarly.restartServer", "icon": "$(debug-restart)"}, {"title": "Pause text check", "category": "Grammarly", "command": "grammarly.pause<PERSON><PERSON><PERSON>", "icon": "$(debug-pause)", "enablement": "grammarly.isActive && !grammarly.isPaused"}, {"title": "Resume text check", "category": "Grammarly", "command": "grammarly.<PERSON><PERSON><PERSON><PERSON>", "icon": "$(debug-start)", "enablement": "grammarly.isActive && grammarly.isPaused"}]}, "license": "MIT", "main": "./dist/extension/index.node.js", "browser": "./dist/extension/index.browser.js", "buildConfig": {"external": ["vscode"], "useMain": false, "sources": {"src/index.ts": [{"format": "esm", "file": "dist/extension/index.mjs"}, {"format": "cjs", "file": "dist/extension/index.node.js", "bundle": {"platform": "node", "external": ["vscode"]}}], "src/server.ts": [{"format": "esm", "file": "dist/server/index.mjs"}, {"format": "cjs", "file": "dist/server/index.node.js", "bundle": {"platform": "node", "conditions": ["node", "import"], "external": ["buffer", "bufferutil", "encoding", "node:buffer", "node:crypto", "node:events", "node:fs", "node:http", "node:https", "node:net", "node:os", "node:path", "node:perf_hooks", "node:process", "node:stream", "node:stream/web", "node:tls", "node:url", "node:util", "node:vm", "node:zlib", "worker_threads", "utf-8-validate"]}}]}}, "files": ["./dist", "./assets"], "scripts": {"build": "node ../scripts/build-extension.mjs", "release": "node ../scripts/publish-extension.mjs"}, "dependencies": {"grammarly-languageclient": "workspace:*", "grammarly-languageserver": "workspace:*"}, "devDependencies": {"@types/vscode": "^1.63.0", "@types/node": "^16.0.0"}}