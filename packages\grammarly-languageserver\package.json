{"name": "grammarly-languageserver", "version": "0.0.4", "description": "LSP server implementation for Grammarly", "author": "<PERSON><PERSON> <<EMAIL>>", "bin": "./bin/server.js", "main": "./dist/index.node.cjs", "module": "./dist/index.node.mjs", "browser": "./dist/index.browser.mjs", "types": "./dist/index.d.ts", "exports": {".": {"node": {"require": "./dist/index.node.cjs", "import": "./dist/index.node.mjs", "default": "./dist/index.node.mjs"}, "default": {"import": "./dist/index.browser.mjs", "default": "./dist/index.browser.mjs"}}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/znck/grammarly", "directory": "packages/grammarly-languageserver"}, "buildConfig": {"useMain": false, "external": ["vscode-languageserver/browser", "vscode-languageserver/node", "node:os", "node:fs", "node:path"], "sources": {"src/index.ts": [{"format": "dts", "file": "dist/index.d.ts"}], "src/index.browser.ts": [{"format": "esm", "file": "dist/index.browser.mjs"}], "src/index.node.ts": [{"format": "esm", "file": "dist/index.node.mjs"}, {"format": "cjs", "file": "dist/index.node.cjs"}]}}, "license": "MIT", "files": ["dist", "bin"], "dependencies": {"@grammarly/sdk": "^2.3.17", "grammarly-richtext-encoder": "workspace:*", "htmlparser2": "^8.0.1", "idb-keyval": "^6.1.0", "inversify": "^6.0.1", "node-fetch": "^2.6.0", "reflect-metadata": "^0.1.13", "vscode-languageserver": "^7.0.0", "vscode-languageserver-textdocument": "^1.0.4", "web-tree-sitter": "^0.20.8"}, "devDependencies": {"domhandler": "^5.0.3"}}