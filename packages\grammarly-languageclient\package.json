{"name": "grammarly-languageclient", "version": "0.0.3", "description": "LSP client implementation for Grammarly", "author": "<PERSON><PERSON> <<EMAIL>>", "main": "./dist/index.node.cjs", "module": "./dist/index.node.mjs", "browser": "./dist/index.browser.mjs", "types": "./dist/index.d.ts", "exports": {".": {"node": {"require": "./dist/index.node.cjs", "import": "./dist/index.node.mjs", "default": "./dist/index.node.mjs"}, "default": {"import": "./dist/index.browser.mjs", "default": "./dist/index.browser.mjs"}}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/znck/grammarly", "directory": "packages/grammarly-languageclient"}, "buildConfig": {"useMain": false, "external": ["vscode-languageclient/browser", "vscode-languageclient/node"], "sources": {"src/index.ts": [{"format": "dts", "file": "dist/index.d.ts"}], "src/index.browser.ts": [{"format": "esm", "file": "dist/index.browser.mjs"}], "src/index.node.ts": [{"format": "esm", "file": "dist/index.node.mjs"}, {"format": "cjs", "file": "dist/index.node.cjs"}]}}, "license": "MIT", "files": ["dist", "bin"], "dependencies": {"vscode-languageclient": "^7.0.0"}, "devDependencies": {"@grammarly/sdk": "^2.3.17"}}