{"private": true, "packageManager": "pnpm@8.10.2", "engines": {"node": "^18.18.2"}, "scripts": {"build": "rollup -c && node scripts/build-web-extension.mjs", "build:wasm": "node scripts/build-wasm.mjs", "watch": "rollup --watch -c", "test": "jest", "release": "changeset version", "open-in-browser": "vscode-test-web --host 127.0.0.1 --browser firefox --extensionDevelopmentPath=./extension ./fixtures"}, "devDependencies": {"@changesets/cli": "^2.22.0", "@netlify/functions": "^1.0.0", "@rollup/plugin-alias": "^3.1.9", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.2.1", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.3.2", "@types/jest": "^27.5.0", "@vscode/test-web": "^0.0.24", "@vuedx/monorepo-tools": "^0.2.2-next-1651055813.0", "esbuild": "^0.14.38", "husky": "^7.0.4", "jest": "^27.0.0", "lint-staged": "^12.4.1", "node-fetch": "^3.3.1", "prettier": "^2.6.2", "rollup": "^2.71.1", "rollup-plugin-copy": "^3.4.0", "semver": "^7.3.7", "tree-sitter-cli": "^0.20.8", "tree-sitter-html": "^0.20.0", "tree-sitter-markdown": "^0.7.1", "tslib": "^2.4.0", "typescript": "^4.6.4", "vsce": "^2.7.0"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,ts,json,yml}": "prettier --write"}, "pnpm": {"neverBuiltDependencies": ["keytar", "tree-sitter-cli", "tree-sitter-html", "tree-sitter-markdown"]}}