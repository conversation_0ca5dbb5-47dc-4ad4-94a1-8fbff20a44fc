{"compilerOptions": {"module": "ESNext", "target": "ES2019", "moduleResolution": "node", "lib": ["ES2019", "WebWorker"], "strict": true, "sourceMap": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUnusedParameters": true, "skipLibCheck": true, "esModuleInterop": true}, "include": ["src/"]}